import { createClient, type SupabaseClient } from "@supabase/supabase-js"

// Database mode configuration
const DATABASE_MODE = process.env.DATABASE_MODE || 'supabase'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

let supabase: SupabaseClient | null = null;
let supabaseError: string | null = null;

// Initialize Supabase (always available for fallback)
if (!supabaseUrl || !supabaseAnonKey) {
  supabaseError = "Vui lòng cấu hình biến môi trường Supabase trong file .env.local";
} else {
    try {
        supabase = createClient(supabaseUrl, supabaseAnonKey)
        console.log(`📊 Supabase client initialized (Mode: ${DATABASE_MODE})`)
    } catch (e: any) {
        supabaseError = "Lỗi khởi tạo Supabase client: " + e.message;
    }
}

export { supabase, supabaseError, DATABASE_MODE };
