if(!self.define){let e,s={};const n=(n,a)=>(n=new URL(n+".js",a).href,s[n]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()}).then(()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e}));self.define=(a,t)=>{const i=e||("document"in self?document.currentScript.src:"")||location.href;if(s[i])return;let c={};const r=e=>n(e,i),f={module:{uri:i},exports:c,require:r};s[i]=Promise.all(a.map(e=>f[e]||r(e))).then(e=>(t(...e),c))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"d3e87b53b260cbf322757957774e9f43"},{url:"/_next/static/chunks/1047-2f770ca6d5271037.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1096.de6bcea5f493d020.js",revision:"de6bcea5f493d020"},{url:"/_next/static/chunks/1283-2bed885c0e072cc3.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1636-0d5e2be044cc7a4d.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1684-2e8a14b0e33c9ab9.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2185-c5708bc2eb98a161.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/2323-10a4c92af8f52290.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/2975-70e7db336d3a678b.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/3664-c2f349dcbef441b7.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/3674-c45fcedb8e624bc1.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/6099-b773e7d076d5d3bb.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/6174-56e356aea3349825.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/6534.44e6688812db5afa.js",revision:"44e6688812db5afa"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/7222-258b4893ffd7996d.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/7279-1436ce8c7f7cb4e6.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/7354.ba3a7a90c664a730.js",revision:"ba3a7a90c664a730"},{url:"/_next/static/chunks/740-ee7caaf7538d8b71.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/7655-1e8baf6825dacb6d.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8214-2525ba73624ab7f2.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8317-2fe537b1963b64c2.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8421-4da9bda6089ae98f.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8881-0430871268202f73.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/8894.cc245b4c85bb3e0b.js",revision:"cc245b4c85bb3e0b"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9748-1dd99519310bf3a8.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/9790.6ef38a4d808fee15.js",revision:"6ef38a4d808fee15"},{url:"/_next/static/chunks/9861.a8374c6efda1123f.js",revision:"a8374c6efda1123f"},{url:"/_next/static/chunks/app/(app)/dashboard/page-8682f90094777733.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/equipment/page-52410952f9afc0b9.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/layout-7f9c7fa31ea4f364.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/maintenance/page-a1540d283d6a2a1d.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-270f18d604436b7e.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-fa3dc9797e1e8856.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/reports/page-825df1788595c138.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/transfers/page-22f831ab37aef0d2.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/(app)/users/page-1dc8a14b75447525.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/layout-924b4b453cc4439f.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/not-found-a1f8e156c24b57fe.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/app/page-45704a69fb6fb3b1.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/main-app-2a87d62be058a59c.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/main-f0c49f4ebb047f5e.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-0376e0a5e5c60c11.js",revision:"hefPeCPFnbHpP-NSaJhG3"},{url:"/_next/static/css/ac7cd0f0be191df7.css",revision:"ac7cd0f0be191df7"},{url:"/_next/static/hefPeCPFnbHpP-NSaJhG3/_buildManifest.js",revision:"2f4508a9a028a5b36fb01832d3719a82"},{url:"/_next/static/hefPeCPFnbHpP-NSaJhG3/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:n,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
